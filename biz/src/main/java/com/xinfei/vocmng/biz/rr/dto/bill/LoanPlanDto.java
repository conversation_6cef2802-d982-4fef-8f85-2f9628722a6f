/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ LoanPlanDto, v 0.1 2024-03-11 19:34 junjie.yan Exp $
 */
@Data
public class LoanPlanDto {

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("借据状态. RP-正常, OD-逾期, FP-结清")
    private String status;

    @ApiModelProperty(value = "总期数")
    private Integer totalTerms;

    @ApiModelProperty(value = "利率")
    private BigDecimal feeRate;

    @ApiModelProperty("借据对应订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty("利息计息方式，TERMLY:按月(期)利率 DAILY: 按日利率")
    private String intCalType;

    @ApiModelProperty("担保费计息方式，TERMLY:按月(期)利率 DAILY: 按日利率")
    private String fee1CalType;

    @ApiModelProperty("逾期还款违约金计息方式，TERMLY:按月(期)利率 DAILY: 按日利率")
    private String onitCalType;

    /**
     * 提前结清费 = 结清挡板费 + 红线减免金额
     */
    @ApiModelProperty("提前结清费(账单列表transDetail的transFee4之和)")
    private BigDecimal advanceSettlementFee;

    @ApiModelProperty("红线减免金额")
    private BigDecimal redDeductAmt;

    @ApiModelProperty("挡板后结清费(realAmtDetail.transFee4)")
    private BigDecimal settleAmtLimit;

    /**
     * 提前结清应还totalAmt = 真实扣款金额明细realAmt + 营销减免金额deductAmt + 红线减免金额redDeductAmt + 非营销减免金额unprofitDeductAmt
     */
    @ApiModelProperty("提前结清应还")
    private BigDecimal totalAmt;

    @ApiModelProperty("提前结清应还（挡板后）")
    private BigDecimal realAmt;

    /**
     * 提前结清应还（优惠减免后） = 提前结清应还 - 营销减免金额 -  非营销减免金额
     * 提前结清应还（优惠减免后） = 真实扣款金额明细 + 红线减免金额
     * 提前结清应还 = 提前结清应还（优惠减免后） + 营销减免金额  + 非营销减免金额
     */
    @ApiModelProperty("提前结清应还（优惠减免后）")
    private BigDecimal totalAmtDiscounts;

    @ApiModelProperty(value = "账单列表")
    private List<PlanDto> planList;

    @ApiModelProperty(value = "费项明细")
    private PlanFeeDetailDto loanFeeDetail;

    @ApiModelProperty("还款计划 还款标识 map key：还款标识(0:待还款,1:已逾期,2:已结清)，value: 期数list")
    private Map<String, List<Integer>> rpyFlagTerm;

    @ApiModelProperty("资方编码")
    private String fundSource;
}